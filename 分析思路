1.libengine.so有可能是包含lua引擎
2.通过搜索luaV_execute中的报错语句，例如：'for' limit must be a number ，找到可能的luaV_execute函数位置，sub_4F4544。
3.跳转路径发现上一个节点是函数sub_4E1DF8，通过与触动精灵引擎对比，可知它有可能是luaD_call 和 resume的结合体
4.sub_4E1DF8的上一个节点分别是sub_4DFA7C、sub_4DFB1C、sub_4DFC84、sub_4E1274、sub_4E35B4、sub_4F363C


lua5.3 官方opcode
  "MOVE",
  "LOADK",
  "LOADKX",
  "LOADBOOL",
  "LOADNIL",
  "GETUPVAL",
  "GETTABUP",
  "GETTABLE",
  "SETTABUP",
  "SETUPVAL",
  "SETTABLE",
  "NEWTABLE",
  "SELF",
  "ADD",
  "SUB",
  "MUL",
  "MOD",
  "POW",
  "DIV",
  "IDIV",
  "BAND",
  "BOR",
  "BXOR",
  "SHL",
  "SHR",
  "UNM",
  "BNOT",
  "NOT",
  "LEN",
  "CONCAT",
  "JMP",
  "EQ",
  "LT",
  "LE",
  "TEST",
  "TESTSET",
  "CALL",
  "TAILCALL",
  "RETURN",
  "FORLOOP",
  "FORPREP",
  "TFORCALL",
  "TFORLOOP",
  "SETLIST",